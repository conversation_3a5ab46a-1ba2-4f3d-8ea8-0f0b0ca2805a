import { plcDataService, PLCPagedResponse } from '@/utils/plc/dataService'
import { MockApiService } from './mock-data'

// 檢查?�否使用Mock模�?
const useMock = (): boolean => {
  return import.meta.env.VITE_USE_MOCK === 'true'
}

// ?��?客戶ID
const getCustomerId = (): string => {
  // ?��?從localStorage?��?
  const storedCustomerId = localStorage.getItem('PLC_CUSTOMER_ID')
  if (storedCustomerId) {
    return storedCustomerId
  }

  // ?�次從環境�??�獲??
  const envCustomerId = import.meta.env.VITE_CUSTOMER_ID
  if (envCustomerId) {
    return envCustomerId
  }

  // ?�後使?��?認�?
  return "fdff1878-a54a-44ee-b82c-a62bdc5cdb55"
}

// 設置客戶ID
export const setCustomerId = (customerId: string): void => {
  localStorage.setItem('PLC_CUSTOMER_ID', customerId)
  console.log('客戶ID已設�?', customerId)
}

/**
 * 標籤管�? API
 * 對�?後端 TagController
 */

// IdNamePair 介面定義
export interface IdNamePair<TId, TName> {
  Id: TId
  Name: TName
}

// 警報例外處理介面
export interface AlarmException {
  Status: boolean
  StartAt: string
  EndAt: string
  Until: IdNamePair<number, string>
  Action: IdNamePair<number, string>
}

// 警報設定介面
export interface TagAlarm {
  Status: number // EnumAlarmStatus
  Audio: boolean
  Sop?: string
  NotifyGroup?: IdNamePair<string, string>[]
  HHStatus?: boolean
  HHValue?: string
  HHContent?: string
  HIStatus?: boolean
  HIValue?: string
  HIContent?: string
  LOStatus?: boolean
  LOValue?: string
  LOContent?: string
  LLStatus?: boolean
  LLValue?: string
  LLContent?: string
  DigAlarmStatus?: boolean
  DigAlarmValue?: number
  DigAlarmContent?: string
  DigNormalStatus?: boolean
  DigNormalValue?: number
  DigNormalContent?: string
  AlarmException?: AlarmException
}

// 完整的標籤資料類型（匹配後端 GetTagListRespDetailTagDetail）
export interface Tag {
  Id: string // TagId
  CurrentValue?: string
  Quality?: boolean
  QualityText?: string
  CustomerId: string
  Device: IdNamePair<string, string> // 設備信息
  Status: boolean
  RegionList?: IdNamePair<string, string>[] // 地區層級列表
  CctvList?: IdNamePair<string, string>[] // CCTV 列表
  Name?: string // 完整測點名稱
  SimpleName?: string // 簡單測點名稱
  Description?: string
  Type: IdNamePair<number, string> // 測點類型
  SaveType: IdNamePair<number, string> // 存取類型
  DataType: IdNamePair<number, string> // 資料類型
  ValueAddress?: string
  DataInterval: number
  IsLog: boolean
  LogInterval: number
  LogInterValType: IdNamePair<number, string>
  InitialValue?: string
  ReTentive: boolean
  TagCategoryList?: IdNamePair<string, string>[][] // 測點分類列表
  RelatedPage?: IdNamePair<string, string>
  Unit: IdNamePair<number, string> // 測量單位
  Ignore?: string
  Alarm: TagAlarm // 警報設定
  IsUseExpression: boolean
  ExpressMode: number
  ExpressValue?: string
}

// 設�?資�?類�?
export interface Device {
  DeviceId: string
  DeviceName: string
  DeviceType: string
  DeviceDescription?: string
  IpAddress?: string
  Port?: number
  Protocol?: string
  IsActive: boolean
  CustomerId: string
  CreatedTime: string
  UpdatedTime?: string
}

// 群�?資�?類�?
export interface TagGroup {
  GroupId: string
  GroupName: string
  GroupDescription?: string
  ParentGroupId?: string
  IsActive: boolean
  CustomerId: string
  CreatedTime: string
  UpdatedTime?: string
}

// 標籤?�詢請�?
export interface GetTagsRequest {
  pageIndex?: number
  pageSize?: number
  tagName?: string
  tagType?: string
  dataType?: string
  deviceId?: string
  groupId?: string
  isActive?: boolean
  customerId?: string
}

// ?�建標籤請�?
export interface CreateTagRequest {
  TagName: string
  TagDescription?: string
  TagType: string
  DataType: string
  Address: string
  DeviceId?: string
  GroupId?: string
  Unit?: string
  MinValue?: number
  MaxValue?: number
  DefaultValue?: number
  IsReadOnly: boolean
  IsActive: boolean
  ScanRate?: number
  AlarmEnabled: boolean
  HighAlarmLimit?: number
  LowAlarmLimit?: number
  HighWarningLimit?: number
  LowWarningLimit?: number
  CustomerId?: string
}

// ?�新標籤請�?
export interface UpdateTagRequest {
  TagId: string
  TagName: string
  TagDescription?: string
  TagType: string
  DataType: string
  Address: string
  DeviceId?: string
  GroupId?: string
  Unit?: string
  MinValue?: number
  MaxValue?: number
  DefaultValue?: number
  IsReadOnly: boolean
  IsActive: boolean
  ScanRate?: number
  AlarmEnabled: boolean
  HighAlarmLimit?: number
  LowAlarmLimit?: number
  HighWarningLimit?: number
  LowWarningLimit?: number
}

// ?��??��?請�?
export interface BatchTagOperationRequest {
  tagIds: string[]
  operation: 'activate' | 'deactivate' | 'delete' | 'export'
  targetGroupId?: string
  targetDeviceId?: string
}

// ?�入標籤請�?
export interface ImportTagsRequest {
  file: File
  overwriteExisting: boolean
  customerId?: string
}

// Region 容器介面
export interface RegionContainer {
  Id: string
  Name: string
  ParentId?: string
  Children?: RegionContainer[]
}

// TagCategory 容器介面
export interface TagCategoryContainer {
  Id: string
  Name: string
  ParentId?: string
  Children?: TagCategoryContainer[]
}

// CCTV 詳細信息介面
export interface CCTVDetail {
  CCTV: IdNamePair<string, string>
  RegionList?: IdNamePair<string, string>[]
}

// GetTagList 完整響應詳細信息（匹配後端 GetTagListRespDetail）
export interface GetTagListRespDetail {
  CheckTagRalatedItemsUpdateTimeOK: boolean
  TagTypeList: IdNamePair<number, string>[]
  SaveTypeList: IdNamePair<number, string>[]
  RelatedPage?: IdNamePair<string, string>
  ExpressionModeList: IdNamePair<number, string>[]
  DataTypeList: IdNamePair<number, string>[]
  LogInterValType: IdNamePair<number, string>[]
  UnitList: IdNamePair<number, string>[]
  AlarmStatusList: IdNamePair<number, string>[]
  AlarmExceptionUntilList: IdNamePair<number, string>[]
  AlarmExceptionActionList: IdNamePair<number, string>[]
  EnumDigitalAlarmValueList: IdNamePair<number, string>[]
  EnumExpressionModeList: IdNamePair<number, string>[]
  RegionHierarchyList?: RegionContainer[]
  TagCategoryHierarchyList?: TagCategoryContainer[]
  CCTVMapList?: CCTVDetail[]
  TagList?: Tag[]
  TagRalatedItemsUpdateTime: string
}

// GetTagList API 響應
export interface GetTagListResponse {
  Message?: string
  ReturnCode: number
  Detail: GetTagListRespDetail
}

// 標籤統計資料
export interface TagStatistics {
  totalTags: number
  activeTags: number
  inactiveTags: number
  alarmEnabledTags: number
  readOnlyTags: number
  deviceCount: number
  groupCount: number
}

/**
 * 標籤管理 API 服務
 */
export const tagsAPI = {
  /**
   * 獲取標籤列表（完整版本，匹配後端 GetTagList API）
   * GET /api/Tag/GetTagList
   * ⚠️ 【重要】API 路徑必須包含 /api 前綴，請勿修改！
   * 📝 對應舊系統：plc-frontend/src/vuex/modules/tags/actionCreator.js 第258行
   */
  getTags: (params?: { TagRalatedItemsUpdateTime?: string }): Promise<GetTagListResponse> => {
    if (useMock()) {
      // 如果使用 Mock，需要轉換格式
      return MockApiService.getTags(params || {}).then(mockResponse => ({
        Message: 'Success',
        ReturnCode: 0,
        Detail: {
          CheckTagRalatedItemsUpdateTimeOK: false,
          TagTypeList: [],
          SaveTypeList: [],
          ExpressionModeList: [],
          DataTypeList: [],
          LogInterValType: [],
          UnitList: [],
          AlarmStatusList: [],
          AlarmExceptionUntilList: [],
          AlarmExceptionActionList: [],
          EnumDigitalAlarmValueList: [],
          EnumExpressionModeList: [],
          RegionHierarchyList: [],
          TagCategoryHierarchyList: [],
          CCTVMapList: [],
          TagList: mockResponse.data || [],
          TagRalatedItemsUpdateTime: new Date().toISOString()
        }
      }))
    }

    // ✅ 調用真實 API，路徑必須包含 /api 前綴
    // 🚫 【禁止修改】不要移除 /api 前綴，否則會返回 HTML 而不是 JSON
    return plcDataService.get('/api/Tag/GetTagList', params)
  },

  /**
   * 獲取標籤列表（簡化版本，用於向後兼容）
   * @deprecated 建議使用 getTags() 獲取完整數據
   */
  getTagsLegacy: (params: GetTagsRequest): Promise<PLCPagedResponse<Tag>> => {
    return tagsAPI.getTags(params).then(response => ({
      data: response.Detail.TagList || [],
      total: response.Detail.TagList?.length || 0,
      page: 1,
      pageSize: response.Detail.TagList?.length || 0
    }))
  },

  /**
   * 獲取標籤詳情
   * GET /api/Tag/GetTag/{tagId}
   */
  getTag: (tagId: string): Promise<Tag> => {
    const customerId = getCustomerId()
    return plcDataService.get(`/api/Tag/GetTag/${tagId}`, { customerId })
  },

  /**
   * 獲取測點屬性（測點用途、接點種類等）
   * GET /api/Tag/GetTagProperties
   * @param tagIds 測點ID數組
   */
  getTagProperties: async (tagIds: string[]): Promise<{ [tagId: string]: { [propertyName: string]: string } }> => {
    if (useMock()) {
      // Mock 數據 - 模擬不同測點的不同用途
      const mockProperties: { [tagId: string]: { [propertyName: string]: string } } = {}
      tagIds.forEach((tagId, index) => {
        // 模擬不同的測點用途
        const usageTypes = ['Normal', 'Alarm', 'Status']
        const contactTypes = ['NO', 'NC']

        mockProperties[tagId] = {
          Usage: usageTypes[index % usageTypes.length], // 輪流分配不同用途
          ContactType: contactTypes[index % contactTypes.length] // 輪流分配不同接點種類
        }
      })
      console.log('🔍 Mock 測點屬性:', mockProperties)
      return Promise.resolve(mockProperties)
    }

    // 由於 nginx 限制請求頭大小，當 Tag ID 數量過多時需要分批處理
    const BATCH_SIZE = 50 // 每批處理 50 個 Tag ID，避免請求頭過大
    const allProperties: { [tagId: string]: { [propertyName: string]: string } } = {}

    console.log('🔍 準備調用 GetTagProperties API:', {
      url: '/Tag/GetTagProperties',
      totalTagCount: tagIds.length,
      batchSize: BATCH_SIZE,
      batchCount: Math.ceil(tagIds.length / BATCH_SIZE)
    })

    // 分批處理 Tag ID
    for (let i = 0; i < tagIds.length; i += BATCH_SIZE) {
      const batchTagIds = tagIds.slice(i, i + BATCH_SIZE)
      const tagsHeader = batchTagIds.join(',')
      const batchIndex = Math.floor(i / BATCH_SIZE) + 1
      const totalBatches = Math.ceil(tagIds.length / BATCH_SIZE)

      console.log(`🔍 處理第 ${batchIndex}/${totalBatches} 批次:`, {
        batchTagIds: batchTagIds.length,
        tagsHeader: tagsHeader.substring(0, 100) + (tagsHeader.length > 100 ? '...' : '')
      })

      try {
        const response = await plcDataService.get('/api/Tag/GetTagProperties', {}, {
          tags: tagsHeader // 將數組轉換為逗號分隔的字符串
        })

        console.log(`🔍 第 ${batchIndex} 批次 API 響應:`, {
          hasDetail: !!response.Detail,
          hasProperties: !!response.Detail?.Properties,
          returnCode: response.ReturnCode,
          message: response.Message,
          propertiesCount: Object.keys(response.Detail?.Properties || {}).length
        })

        // 合併批次結果
        const batchProperties = response.Detail?.Properties || {}
        Object.assign(allProperties, batchProperties)

      } catch (error: any) {
        console.error(`❌ 第 ${batchIndex} 批次 GetTagProperties API 調用失敗:`, error)
        console.error('❌ 錯誤詳情:', {
          message: error.message,
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data
        })
        // 繼續處理下一批次，不中斷整個流程
      }
    }

    console.log('🔍 所有批次處理完成，合併結果:', {
      totalProperties: Object.keys(allProperties).length,
      requestedTags: tagIds.length
    })

    return allProperties
  },

  /**
   * ?�建標籤
   * POST /api/Tag/CreateTag
   */
  createTag: (data: CreateTagRequest): Promise<{ success: boolean; message: string; tagId?: string }> => {
    const customerId = getCustomerId()
    const requestData = { ...data, CustomerId: customerId }
    return plcDataService.post('/Tag/CreateTag', requestData)
  },

  /**
   * ?�新標籤
   * PUT /api/Tag/UpdateTag
   */
  updateTag: (data: UpdateTagRequest): Promise<{ success: boolean; message: string }> => {
    const customerId = getCustomerId()
    const requestData = { ...data, CustomerId: customerId }
    return plcDataService.put('/Tag/UpdateTag', requestData)
  },

  /**
   * 刪除標籤
   * POST /api/Tag/DeleteTag
   * 注意：此 API 要求使用 multipart/form-data 格式
   */
  deleteTag: (tagId: string): Promise<{ success: boolean; message: string }> => {
    // 🔧 修復：使用 FormData 格式以符合 API 要求的 multipart/form-data
    const formData = new FormData()
    formData.append('TagId', tagId)

    return plcDataService.post('/api/Tag/DeleteTag', formData)
  },

  /**
   * ?��??��?標籤
   * POST /api/Tag/BatchOperation
   */
  batchOperation: (data: BatchTagOperationRequest): Promise<{ success: boolean; message: string; affectedCount?: number }> => {
    const customerId = getCustomerId()
    const requestData = { ...data, CustomerId: customerId }
    return plcDataService.post('/api/Tag/BatchOperation', requestData)
  },

  /**
   * ?�入標籤
   * POST /api/Tag/ImportTags
   */
  importTags: (data: ImportTagsRequest): Promise<{ success: boolean; message: string; importedCount?: number; errorCount?: number }> => {
    const customerId = getCustomerId()
    const formData = new FormData()
    formData.append('file', data.file)
    formData.append('overwriteExisting', data.overwriteExisting.toString())
    formData.append('customerId', customerId)
    return plcDataService.postForm('/api/Tag/ImportTags', formData)
  },

  /**
   * ?�出標籤
   * GET /api/Tag/ExportTags
   */
  exportTags: (params: { tagIds?: string[]; customerId?: string }): Promise<Blob> => {
    const customerId = getCustomerId()
    const requestParams = { ...params, customerId }
    return plcDataService.get('/api/Tag/ExportTags', requestParams)
  },

  /**
   * ?��?標籤統�?
   * GET /api/Tag/GetTagStatistics
   */
  getTagStatistics: (customerId?: string): Promise<TagStatistics> => {
    if (useMock()) {
      return MockApiService.getTagStatistics()
    }
    const customerIdToUse = customerId || getCustomerId()
    return plcDataService.get('/api/Tag/GetTagStatistics', { customerId: customerIdToUse })
  },

  /**
   * ?��?設�??�表
   * GET /api/Tag/GetDeviceList (?��?後端確�?此API存在)
   */
  getDevices: (customerId?: string): Promise<Device[]> => {
    if (useMock()) {
      return MockApiService.getDevices()
    }
    const customerIdToUse = customerId || getCustomerId()
    return plcDataService.get('/api/Tag/GetDeviceList', { customerId: customerIdToUse })
  },

  /**
   * ?��?設�??��??�層?�表
   * GET /api/Tag/GetDeviceCategoryHierarchyList
   */
  getDeviceCategoryHierarchy: (customerId?: string): Promise<any> => {
    if (useMock()) {
      return Promise.resolve([])
    }
    const customerIdToUse = customerId || getCustomerId()
    return plcDataService.get('/api/Tag/GetDeviceCategoryHierarchyList', { customerId: customerIdToUse })
  },

  /**
   * ?��?設�??��?
   * POST /api/Tag/CreateNewDeviceCategory
   */
  createDeviceCategory: (data: any): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/api/Tag/CreateNewDeviceCategory', data)
  },

  /**
   * ?�新設�??��?
   * POST /api/Tag/UpdateDeviceCategory
   */
  updateDeviceCategory: (data: any): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/api/Tag/UpdateDeviceCategory', data)
  },

  /**
   * ?�除設�??��?
   * POST /api/Tag/DeleteDeviceCategory
   */
  deleteDeviceCategory: (data: any): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/api/Tag/DeleteDeviceCategory', data)
  },

  /**
   * ?�除設�?
   * POST /api/Tag/DeleteDevice
   */
  deleteDevice: (data: { DeviceId: string }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/api/Tag/DeleteDevice', data)
  },

  /**
   * 刪除測點
   * POST /api/Tag/DeleteTag
   */
  // 移除重複的 deleteTag - 已在第 232 行定義

  /**
   * 刪除測點類別
   * POST /api/Tag/DeleteTagCategory
   * 注意：使用 application/x-www-form-urlencoded 格式（與舊系統一致）
   */
  deleteTagCategory: (data: { Id: string }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }

    // 🔧 修復：使用 URLSearchParams 格式（與舊系統一致）
    // 舊系統使用 qs.stringify() 轉換為 application/x-www-form-urlencoded
    const urlParams = new URLSearchParams()
    urlParams.append('Id', data.Id)

    return plcDataService.post('/api/Tag/DeleteTagCategory', urlParams)
  },

  /**
   * 刪除地區
   * POST /api/Tag/DeleteRegion
   * 注意：此 API 要求使用 multipart/form-data 格式
   */
  deleteRegion: (data: { RegionId: string }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }

    // 🔧 修復：使用 FormData 格式以符合 API 要求的 multipart/form-data
    const formData = new FormData()
    formData.append('RegionId', data.RegionId)

    return plcDataService.post('/api/Tag/DeleteRegion', formData)
  },

  /**
   * ?�除?��?
   * POST /api/Tag/DeleteTagChannel
   * 注意：此 API 要求使用 multipart/form-data 格式
   */
  deleteTagChannel: (data: { TagChannelId: string }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }

    // 🔧 修復：使用 FormData 格式以符合 API 要求的 multipart/form-data
    const formData = new FormData()
    formData.append('TagChannelId', data.TagChannelId)

    return plcDataService.post('/api/Tag/DeleteTagChannel', formData)
  },

  /**
   * ?�除群�?
   * POST /api/Tag/DeleteGroup
   */
  deleteGroup: (data: { GroupId: string }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/api/Tag/DeleteGroup', data)
  },

  // ==================== ?��?/編輯?�能 ====================

  /**
   * 新增測點分類
   * POST /api/Tag/CreateNewTagCategory
   */
  createTagCategory: (data: { CategoryName: string; ParentId?: string }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    // 根據舊系統實現，使用 application/x-www-form-urlencoded 格式
    const requestData = new URLSearchParams()
    requestData.append('CategoryName', data.CategoryName)
    // 確保 ParentId 不是 undefined，頂層分類使用空字符串
    const parentId = data.ParentId && data.ParentId !== 'undefined' ? data.ParentId : ''
    requestData.append('ParentId', parentId)
    return plcDataService.post('/api/Tag/CreateNewTagCategory', requestData)
  },

  /**
   * 更新測點分類
   * POST /api/Tag/UpdateTagCategory
   */
  updateTagCategory: (data: { Id: string; Name: string }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    // 根據舊系統實現，使用 application/x-www-form-urlencoded 格式
    const requestData = new URLSearchParams()
    requestData.append('Id', data.Id)
    requestData.append('Name', data.Name)
    return plcDataService.post('/api/Tag/UpdateTagCategory', requestData)
  },

  /**
   * ?��??��?
   * POST /api/Tag/CreateNewRegion
   */
  createRegion: (data: { ParentId?: string; RegionName: string }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/api/Tag/CreateNewRegion', data)
  },

  /**
   * ?�新?��?
   * POST /api/Tag/UpdateRegion
   */
  updateRegion: (data: { RegionId: string; RegionName: string }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/api/Tag/UpdateRegion', data)
  },

  /**
   * ?��??��?
   * POST /api/Tag/CreateNewTagChannel
   */
  createTagChannel: (data: { Name: string; Description?: string; ChannelType?: string }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/api/Tag/CreateNewTagChannel', data)
  },

  /**
   * ?�新?��?
   * POST /api/Tag/UpdateTagChannel
   */
  updateTagChannel: (data: { Id: string; Name: string; Description?: string; ChannelType?: string }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/api/Tag/UpdateTagChannel', data)
  },

  /**
   * ?��?群�?
   * POST /api/Tag/CreateNewGroup
   */
  createGroup: (data: { Name: string; GroupCategoryId?: string; Description?: string; TagIds?: string[] }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/api/Tag/CreateNewGroup', data)
  },

  /**
   * ?�新群�?
   * POST /api/Tag/UpdateGroup
   */
  updateGroup: (data: { Id: string; Name: string; GroupCategoryId?: string; Description?: string; TagIds?: string[] }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/api/Tag/UpdateGroup', data)
  },

  /**
   * ?��?群�??��?
   * POST /api/Tag/CreateNewGroupCategory
   */
  createGroupCategory: (data: { Name: string; ParentId?: string; Description?: string }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/api/Tag/CreateNewGroupCategory', data)
  },

  /**
   * ?�新群�??��?
   * POST /api/Tag/UpdateGroupCategory
   */
  updateGroupCategory: (data: { Id: string; Name: string; ParentId?: string; Description?: string }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/api/Tag/UpdateGroupCategory', data)
  },

  /**
   * ?��?裝置
   * POST /api/Tag/CreateNewDevice
   */
  createDevice: (data: { Name: string; DeviceCategoryId?: string; TagChannelId?: string; Description?: string }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/Tag/CreateNewDevice', data)
  },

  /**
   * ?�新裝置
   * POST /api/Tag/UpdateDevice
   */
  updateDevice: (data: { Id: string; Name: string; DeviceCategoryId?: string; TagChannelId?: string; Description?: string }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/Tag/UpdateDevice', data)
  },

  /**
   * ??群??表
   * GET /api/Tag/GetGroups
   */
  getGroups: (customerId?: string): Promise<TagGroup[]> => {
    if (useMock()) {
      return MockApiService.getGroups()
    }
    const customerIdToUse = customerId || getCustomerId()
    return plcDataService.get('/api/Tag/GetGroups', { customerId: customerIdToUse })
  },

  /**
   * ??標籤類???
   * GET /api/Tag/GetTagTypes
   */
  getTagTypes: (): Promise<{ value: string; label: string }[]> => {
    if (useMock()) {
      return MockApiService.getTagTypes()
    }
    const customerId = getCustomerId()
    return plcDataService.get('/api/Tag/GetTagTypes', { customerId })
  },

  /**
   * ??資?類???
   * GET /api/Tag/GetDataTypes
   */
  getDataTypes: (): Promise<{ value: string; label: string }[]> => {
    if (useMock()) {
      return MockApiService.getDataTypes()
    }
    const customerId = getCustomerId()
    return plcDataService.get('/api/Tag/GetDataTypes', { customerId })
  },

  /**
   * 驗?標籤?稱???
   * GET /api/Tag/ValidateTagName
   */
  validateTagName: (tagName: string, excludeTagId?: string): Promise<{ isValid: boolean; message?: string }> => {
    const customerId = getCustomerId()
    return plcDataService.get('/api/Tag/ValidateTagName', { tagName, excludeTagId, customerId })
  },

  /**
   * ?��?標籤?�歷??
   * GET /api/Tag/GetTagValueHistory
   */
  getTagValueHistory: (params: {
    tagId: string
    startTime?: string
    endTime?: string
    pageIndex?: number
    pageSize?: number
  }): Promise<PLCPagedResponse<{
    TagId: string
    Value: any
    Quality: string
    Timestamp: string
  }>> => {
    const customerId = getCustomerId()
    const requestParams = { ...params, customerId }
    return plcDataService.get('/api/Tag/GetTagValueHistory', requestParams)
  },

  /**
   * ?��?標籤?��???
   * GET /api/Tag/GetTagRealTimeValue
   */
  getTagRealTimeValue: (tagId: string): Promise<{
    TagId: string
    TagName: string
    Value: any
    Quality: string
    Timestamp: string
    Unit?: string
  }> => {
    const customerId = getCustomerId()
    return plcDataService.get(`/api/Tag/GetTagRealTimeValue/${tagId}`, { customerId })
  },

  /**
   * 寫入標籤??
   * POST /api/Tag/WriteTagValue
   */
  writeTagValue: (data: {
    TagId: string
    Value: any
    CustomerId?: string
  }): Promise<{ success: boolean; message: string }> => {
    const customerId = getCustomerId()
    const requestData = { ...data, CustomerId: customerId }
    return plcDataService.post('/api/Tag/WriteTagValue', requestData)
  },

  // ==================== ?�?�更?��???====================

  /**
   * 設�??��??�??
   * POST /api/Tag/SetTagChannelAbility
   */
  setChannelStatus: (data: { TagChannelId: string; TargetStatus: boolean }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/api/Tag/SetTagChannelAbility', data)
  },

  /**
   * 設�?裝置?�??
   * POST /api/Tag/SetTagDeviceAbility
   */
  setDeviceStatus: (data: { DeviceId: string; TargetStatus: boolean }): Promise<any> => {
    if (useMock()) {
      return Promise.resolve({ success: true })
    }
    return plcDataService.post('/api/Tag/SetTagDeviceAbility', data)
  }
}

// ?��??��??�口
export interface RegionItem {
  id: string
  name: string
  code?: string
  description?: string
  parentId?: string
  status: 'active' | 'inactive'
  hasChildren?: boolean
  childCount?: number
  sortOrder?: number
  createTime?: string
  children?: RegionItem[]
}

// 測�??��??�口（新系統�?
export interface TagItem {
  id: string
  name: string
  description?: string
  tagType: 'Analog' | 'Digital' | 'Calculated'
  dataType: 'Boolean' | 'Int16' | 'Int32' | 'Float' | 'Double' | 'String'
  address: string
  deviceId: string
  deviceName?: string
  unit?: string
  minValue?: number
  maxValue?: number
  defaultValue?: number
  scanRate?: number
  status: 'active' | 'inactive'
  isReadOnly?: boolean
  alarmEnabled?: boolean
  highAlarmLimit?: number
  lowAlarmLimit?: number
  highWarningLimit?: number
  lowWarningLimit?: number
  currentValue?: any
  updateTime?: string
}

// 測點分類接口
export interface TagClassItem {
  id: string
  name: string
  parentId?: string
  description?: string
  children?: TagClassItem[]
}

// 單位接口
export interface UnitItem {
  Id: string
  Name: string
  Symbol?: string
  Description?: string
}

// 地區 API
export const regionAPI = {
  // 取得地區列表
  getRegionList: (params: any) => plcDataService.get('/api/Tag/GetRegionHierarchyList', { params }),

  // 建立地區
  createRegion: (data: any) => plcDataService.post('/api/regions', data),

  // 更新地區
  updateRegion: (id: string, data: any) => plcDataService.put(`/api/regions/${id}`, data),

  // 刪除地區
  deleteRegion: (id: string) => plcDataService.delete(`/api/regions/${id}`),

  // 取得地區樹狀
  getRegionTree: () => plcDataService.get('/api/Tag/GetRegionHierarchyList')
}

// 單位 API
export const unitAPI = {
  // 取得單位列表 - 從 GetTagList API 的 UnitList 屬性獲取
  getUnitList: (params?: { TagRalatedItemsUpdateTime?: string }): Promise<GetTagListResponse> => {
    return plcDataService.get('/api/Tag/GetTagList', params)
  },

  // 建立單位
  createUnit: (data: any) => plcDataService.post('/api/units', data),

  // 更新單位
  updateUnit: (id: string, data: any) => plcDataService.put(`/api/units/${id}`, data),

  // 刪除單位
  deleteUnit: (id: string) => plcDataService.delete(`/api/units/${id}`)
}

// 測??? API（新系統�?
export const tagAPI = {
  // ?��?測�??�表
  getTagList: (params: any) => plcDataService.get('/api/tags', { params }),

  // ?�建測�?
  createTag: (data: any) => plcDataService.post('/api/tags', data),

  // ?�新測�?
  updateTag: (id: string, data: any) => plcDataService.put(`/api/tags/${id}`, data),

  // ?�除測�?
  deleteTag: (id: string) => plcDataService.delete(`/api/tags/${id}`),

  // ?��?測�?詳�?
  getTagDetail: (id: string) => plcDataService.get(`/api/tags/${id}`),

  // ?��?測�??��?�?
  getTagClassTree: () => plcDataService.get('/api/tags/classes/tree'),

  // ?�建測�??��?
  createTagClass: (data: any) => plcDataService.post('/api/tags/classes', data),

  // ?�新測�??��?
  updateTagClass: (id: string, data: any) => plcDataService.put(`/api/tags/classes/${id}`, data),

  // ?�除測�??��?
  deleteTagClass: (id: string) => plcDataService.delete(`/api/tags/classes/${id}`)
}
