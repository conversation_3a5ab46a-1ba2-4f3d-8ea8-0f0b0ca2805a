import { plcHttp } from '@/utils/plc/http'

/**
 * PLC DataService - 統一的 API 請求服務
 * 完全對應後端 API 結構，確保 100% 相容性
 */
export class PLCDataService {
  private baseURL: string

  constructor(baseURL: string = '') {
    this.baseURL = baseURL
  }

  // GET 請求
  async get<T>(url: string, params?: any, headers?: any): Promise<T> {
    const config: any = {}
    if (params) {
      config.params = params
    }
    if (headers) {
      config.headers = headers
    }
    const response = await plcHttp.get<T>(`${this.baseURL}${url}`, config)
    return response.data
  }

  // POST 請求 - 自動檢測數據格式
  async post<T>(url: string, data?: any): Promise<T> {
    let headers: any = {}

    // 🔧 修復：根據數據類型自動選擇正確的 Content-Type
    if (data instanceof URLSearchParams) {
      // URLSearchParams 使用 application/x-www-form-urlencoded（與舊系統一致）
      headers['Content-Type'] = 'application/x-www-form-urlencoded'
    } else if (data instanceof FormData) {
      // FormData 不設置 Content-Type，讓瀏覽器自動設置正確的 boundary
      // headers['Content-Type'] 保持 undefined，瀏覽器會自動設置為 multipart/form-data
    } else {
      // 其他情況使用 JSON
      headers['Content-Type'] = 'application/json'
    }

    const response = await plcHttp.post<T>(`${this.baseURL}${url}`, data, { headers })
    return response.data
  }

  // PUT 請求
  async put<T>(url: string, data?: any): Promise<T> {
    const response = await plcHttp.put<T>(`${this.baseURL}${url}`, data)
    return response.data
  }

  // DELETE 請求
  async delete<T>(url: string): Promise<T> {
    const response = await plcHttp.delete<T>(`${this.baseURL}${url}`)
    return response.data
  }

  // POST 請求 (FormData)
  async postForm<T>(url: string, formData: FormData): Promise<T> {
    const response = await plcHttp.post<T>(`${this.baseURL}${url}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data
  }
}

// 統一的響應格式接口（對應後端）
export interface PLCApiResponse<T> {
  Success: boolean
  Message: string
  Detail: T
  ErrorCode?: string
}

// 分頁響應格式
export interface PLCPagedResponse<T> {
  Items: T[]
  TotalCount: number
  PageIndex: number
  PageSize: number
}

// 創建全局實例 - 不使用前綴，讓 Vite 代理處理
export const plcDataService = new PLCDataService('')

// 導出類型
export type { PLCDataService }