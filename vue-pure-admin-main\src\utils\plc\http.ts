import Axios, { type AxiosInstance, type AxiosRequestConfig } from "axios";
import { stringify } from "qs";
import NProgress from "../progress";
import { 
  getPLCToken, 
  formatPLCToken, 
  isPLCTokenValid,
  removePLCToken 
} from "@/utils/plc/auth";
import { usePLCAuthStore } from "@/store/modules/plc-auth";
import { ElMessage } from "element-plus";
import router from "@/router";

/**
 * PLC HTTP 客戶端
 * 專門處理 PLC 後端 API 請求，確保完全相容
 */

// ⚠️ 【重要】HTTP 客戶端默認配置 - 請勿修改此區塊！
const defaultConfig: AxiosRequestConfig = {
  // ✅ 開發環境使用空 baseURL 讓 Vite 代理處理，生產環境使用完整 URL
  // 🚫 【禁止】不要設置為 "/api"，否則會造成雙重前綴問題
  // 📝 參考舊系統：plc-frontend/src/config/dataService/dataService.js 第14行
  baseURL: import.meta.env.DEV ? "" : (import.meta.env.VITE_API_BASE_URL || "http://192.168.1.152:8345"),
  timeout: 30000, // PLC 系統可能需要較長的響應時間
  headers: {
    Accept: "application/json, text/plain, */*",
    "Content-Type": "application/json",
    "X-Requested-With": "XMLHttpRequest"
  },
  paramsSerializer: {
    serialize: stringify
  }
};

class PLCHttp {
  private axiosInstance: AxiosInstance;
  private static requests: Array<(token: string) => void> = [];
  private static isRefreshing = false;

  constructor() {
    this.axiosInstance = Axios.create(defaultConfig);
    this.setupInterceptors();
  }

  /**
   * 設置攔截器
   */
  private setupInterceptors(): void {
    this.setupRequestInterceptor();
    this.setupResponseInterceptor();
  }

  /**
   * 請求攔截器
   */
  private setupRequestInterceptor(): void {
    this.axiosInstance.interceptors.request.use(
      async (config) => {
        // 開啟進度條
        NProgress.start();

        // 白名單：不需要 Token 的接口
        const whiteList = ["/Identity/Login", "/Identity/RefreshToken", "/api/Staff/StaffLogin"];
        const isWhitelisted = whiteList.some(url => config.url?.includes(url));

        if (isWhitelisted) {
          return config;
        }

        // 處理 Token - 支援舊前端的token存儲方式
        return new Promise((resolve) => {
          // 優先使用舊前端方式存儲的token
          const legacyToken = localStorage.getItem('access_token');

          if (legacyToken) {
            // 使用舊前端方式的token
            config.headers.Authorization = `Bearer ${legacyToken}`;
            console.log('使用舊前端token:', legacyToken.substring(0, 20) + '...');
            resolve(config);
            return;
          }

          // 回退到新系統的token管理
          const tokenInfo = getPLCToken();

          if (tokenInfo && isPLCTokenValid(tokenInfo.accessToken)) {
            // Token 有效，直接使用
            config.headers.Authorization = formatPLCToken(tokenInfo.accessToken);
            resolve(config);
          } else if (tokenInfo?.refreshToken && !PLCHttp.isRefreshing) {
            // Token 過期，嘗試刷新
            PLCHttp.isRefreshing = true;
            this.refreshToken(tokenInfo.refreshToken)
              .then((newToken) => {
                config.headers.Authorization = formatPLCToken(newToken);
                PLCHttp.requests.forEach(cb => cb(newToken));
                PLCHttp.requests = [];
                resolve(config);
              })
              .catch(() => {
                this.handleAuthError();
                resolve(config);
              })
              .finally(() => {
                PLCHttp.isRefreshing = false;
              });
          } else if (PLCHttp.isRefreshing) {
            // 正在刷新 Token，等待
            PLCHttp.requests.push((token: string) => {
              config.headers.Authorization = formatPLCToken(token);
              resolve(config);
            });
          } else {
            // 沒有有效 Token，直接發送請求（可能會收到 401）
            resolve(config);
          }
        });
      },
      (error) => {
        NProgress.done();
        return Promise.reject(error);
      }
    );
  }

  /**
   * 響應攔截器
   */
  private setupResponseInterceptor(): void {
    this.axiosInstance.interceptors.response.use(
      (response) => {
        NProgress.done();

        // 處理 PLC 後端的統一響應格式
        const { data } = response;

        // 🔧 修復：檢查 PLC API 的 ReturnCode 格式（與舊系統一致）
        if (data && typeof data === 'object' && 'ReturnCode' in data) {
          // 檢查 ReturnCode，1 表示成功，其他值表示錯誤
          if (Number(data.ReturnCode) !== 1) {
            // 後端返回業務錯誤
            const errorMessage = data.Message || '請求失敗';
            console.log('API 錯誤，ReturnCode:', data.ReturnCode, 'Message:', errorMessage);

            // 🚫 不在這裡顯示 ElMessage，讓前端組件自己處理錯誤顯示
            // ElMessage.error(errorMessage);

            // 如果是認證相關錯誤，清除 Token
            if (data.ErrorCode === 'AUTH_FAILED' || data.ErrorCode === 'TOKEN_EXPIRED') {
              this.handleAuthError();
            }

            // 🔧 重要：不拋出錯誤，而是返回原始響應讓前端組件處理
            // 這樣前端可以檢查 ReturnCode 並顯示適當的錯誤訊息
            return response;
          }

          // 成功響應，返回原始響應（保持 ReturnCode 等資訊）
          return response;
        }

        // 檢查是否為新格式的 PLC API 響應（包含 Success 欄位）
        if (data && typeof data === 'object' && 'Success' in data) {
          if (!data.Success) {
            // 後端返回業務錯誤
            const errorMessage = data.Message || '請求失敗';
            ElMessage.error(errorMessage);

            // 如果是認證相關錯誤，清除 Token
            if (data.ErrorCode === 'AUTH_FAILED' || data.ErrorCode === 'TOKEN_EXPIRED') {
              this.handleAuthError();
            }

            return Promise.reject(new Error(errorMessage));
          }

          // 成功響應，返回 Detail 部分
          return { ...response, data: data.Detail };
        }

        // 非 PLC API 響應，直接返回
        return response;
      },
      (error) => {
        NProgress.done();

        // 處理 HTTP 錯誤
        if (error.response) {
          const { status, data } = error.response;
          
          switch (status) {
            case 401:
              // 未授權，清除 Token 並跳轉登入頁
              this.handleAuthError();
              ElMessage.error('登入已過期，請重新登入');
              break;
              
            case 403:
              ElMessage.error('沒有權限執行此操作');
              break;
              
            case 404:
              ElMessage.error('請求的資源不存在');
              break;
              
            case 500:
              const serverError = data?.Message || data?.message || '伺服器內部錯誤';
              console.error('500 錯誤詳情:', data);
              ElMessage.error(serverError);
              break;
              
            default:
              const message = data?.Message || data?.message || `請求失敗 (${status})`;
              ElMessage.error(message);
          }
        } else if (error.request) {
          ElMessage.error('網路連接失敗，請檢查網路設定');
        } else {
          ElMessage.error(error.message || '請求失敗');
        }

        return Promise.reject(error);
      }
    );
  }

  /**
   * 刷新 Token
   */
  private async refreshToken(refreshToken: string): Promise<string> {
    try {
      const response = await this.axiosInstance.post('/api/Identity/RefreshToken', {
        refreshToken
      });
      
      const { accessToken, refreshToken: newRefreshToken, expires } = response.data;
      
      // 更新 Store 中的 Token
      const authStore = usePLCAuthStore();
      const currentUserInfo = authStore.userInfo;
      
      authStore.setAuthData({
        accessToken,
        refreshToken: newRefreshToken,
        expires: new Date(expires).getTime(),
        userInfo: currentUserInfo
      });
      
      return accessToken;
    } catch (error) {
      console.error('刷新 Token 失敗:', error);
      throw error;
    }
  }

  /**
   * 處理認證錯誤
   */
  private handleAuthError(): void {
    const authStore = usePLCAuthStore();
    authStore.clearAuthData();
    
    // 跳轉到登入頁
    if (router.currentRoute.value.path !== '/login') {
      router.push('/login');
    }
  }

  /**
   * GET 請求
   */
  get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.axiosInstance.get(url, config);
  }

  /**
   * POST 請求
   */
  post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.axiosInstance.post(url, data, config);
  }

  /**
   * PUT 請求
   */
  put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.axiosInstance.put(url, data, config);
  }

  /**
   * DELETE 請求
   */
  delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.axiosInstance.delete(url, config);
  }

  /**
   * PATCH 請求
   */
  patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.axiosInstance.patch(url, data, config);
  }
}

// 創建 PLC HTTP 實例
export const plcHttp = new PLCHttp();

// 導出類型
export type { PLCHttp };
